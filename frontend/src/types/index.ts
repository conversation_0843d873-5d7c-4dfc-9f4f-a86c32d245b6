// User types
export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  avatar?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  role?: string;
}

export interface UpdateUserRequest {
  username?: string;
  email?: string;
  role?: string;
  status?: string;
  avatar?: string;
}

// Category types
export interface Category {
  id: number;
  name: string;
  description: string;
  color: string;
  icon: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface CategoryWithStats extends Category {
  video_count: number;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
  status?: string;
}

// Video types
export interface Video {
  id: number;
  title: string;
  description: string;
  filename: string;
  file_path: string;
  thumbnail?: string;
  duration: number;
  file_size: number;
  mime_type: string;
  category_id: number;
  user_id: number;
  views: number;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface VideoWithDetails extends Video {
  category?: Category;
  user?: User;
}

export interface CreateVideoRequest {
  title: string;
  description?: string;
  category_id: number;
}

export interface UpdateVideoRequest {
  title?: string;
  description?: string;
  category_id?: number;
  status?: string;
}

export interface VideoStats {
  total_videos: number;
  published_videos: number;
  processing_videos: number;
  total_views: number;
  total_storage: number;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    videos?: T[];
    categories?: T[];
    users?: T[];
    pagination: {
      current_page: number;
      total_pages: number;
      total_items: number;
      limit: number;
    };
  };
}

// Auth state
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

// UI state
export interface UIState {
  sidebarOpen: boolean;
  currentSection: string;
  loading: boolean;
  error: string | null;
}
